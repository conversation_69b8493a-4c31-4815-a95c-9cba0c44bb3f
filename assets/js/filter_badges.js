// JS para exibir badges dos filtros selecionados e contador no ícone do modal
$(function () {
  /**
   * Gera o HTML para um único badge de filtro.
   * @param {string} filterName - O nome/chave do filtro para o botão de remoção.
   * @param {string} textoCompleto - O texto a ser exibido no tooltip.
   * @param {string} textoCurto - O texto truncado a ser exibido no badge.
   * @returns {string} O HTML do badge.
   */
  function criarBadgeHTML(filterName, textoCompleto, textoCurto) {
    // Usando template literals (`) para um código mais limpo e seguro.
    const textoTooltip = textoCompleto.replace(/"/g, "&quot;");
    return `
      <span 
        class="badge badge-primary mr-1 d-inline-flex align-items-center filter-badge" 
        data-tooltip="${textoTooltip}" 
        style="max-width: 220px;"
      >
        <span class="badge-text" style="flex: 1; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
          ${textoCurto}
        </span>
        <span 
          class="badge badge-light badge-remove ml-1" 
          data-name="${filterName}" 
          style="cursor: pointer;"
        >
          &times;
        </span>
      </span>
    `;
  }

  // Função para extrair filtros selecionados do formulário
  function getFiltrosSelecionados() {
    let filtros = [];
    $("#filterForm")
      .find("input, select")
      .each(function () {
        let $el = $(this);
        let name = $el.attr("name");
        let type = $el.attr("type");
        let label = $el.closest(".form-group").find("label").text();
        let value = $el.val();
        if (type === "checkbox") {
          if ($el.is(":checked")) {
            filtros.push({ name: name, label: label, value: "Sim" });
          }
        } else if ($el.is("select")) {
          let selected = $el
            .find("option:selected")
            .map(function () {
              return $(this).text();
            })
            .get();
          if (
            selected.length &&
            selected[0] !== "" &&
            selected[0] !== "Selecione..."
          ) {
            filtros.push({
              name: name,
              label: label,
              value: selected.join(", "),
            });
          }
        } else if (type === "date" || type === "text") {
          if (value) {
            filtros.push({ name: name, label: label, value: value });
          }
        }
      });
    return filtros;
  }

  // Renderiza os badges dos filtros selecionados
  function renderBadges() {
    let filtros = getFiltrosSelecionados();
    let $container = $("#active-filters");
    $container.empty();
    filtros.forEach(function (filtro) {
      const texto = filtro.label + ": " + filtro.value;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;
      
      const badgeHTML = criarBadgeHTML(filtro.name, texto, textoCurto);
      $container.append(badgeHTML);
    });

    // Atualiza contador no ícone do modal
    let $countBadge = $("#filter-count-badge");
    if (filtros.length > 0) {
      $countBadge.text(filtros.length).show();
    } else {
      $countBadge.hide();
    }
  }
  
    // Função utilitária para validar se um filtro tem valor válido
  function isValidFilterValue(value) {
    if (Array.isArray(value)) {
      const nonEmptyValues = value.filter(
        (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
      );
      return nonEmptyValues.length > 0;
    }
    return (
      value && value !== "" && value !== null && value !== false && value !== "0" && value !== undefined
    );
  }

  // Função para renderizar badges baseado nos filtros salvos do backend
  function renderBadgesFromSavedFilters() {
    if (
      typeof FilterModalIntegration === "undefined" ||
      !FilterModalIntegration.getSavedFilters
    ) {
      return false;
    }

    const savedFilters = FilterModalIntegration.getSavedFilters();
    const $container = $("#active-filters");
    $container.empty();
    let totalFilters = 0;

    const filterLabels = {
      pacotes_eventos: "Pacotes / Eventos",
      sistema_origem: "Sistema de origem",
      owner: "Owner",
      prioridade: "Prioridade",
      atribuido_para: "Atribuídos para",
      status_classificacao_fiscal: "Status de classificação fiscal",
      triagem_diana_falha: "Falha na triagem",
      novo_material: "Novo material",
      estabelecimento: "Estabelecimento",
      importado: "Importado",
      farol_sla: "Farol SLA",
      data_criacao_from: "Data de criação (de)",
      data_criacao_to: "Data de criação (até)",
      data_modificacao_from: "Data de modificação (de)",
      data_modificacao_to: "Data de modificação (até)",
      data_importado_from: "Data que tornou-se importado (de)",
      data_importado_to: "Data que tornou-se importado (até)",
    };

    Object.keys(savedFilters).forEach(function (filterName) {
      const value = savedFilters[filterName];
      const label = filterLabels[filterName];

      if (!label || !isValidFilterValue(value)) return;

      let displayValue = "";
      if (Array.isArray(value)) {
        const nonEmptyValues = value.filter(
          (v) => v && v !== "" && v !== "-1" && v !== null && v !== undefined
        );
        displayValue = nonEmptyValues.join(", ");
      } else {
        displayValue = (filterName === "triagem_diana_falha") ? "Sim" : value;
      }
      
      const texto = label + ": " + displayValue;
      const textoCurto =
        texto.length > 32 ? texto.substring(0, 29) + "..." : texto;

      const badgeHTML = criarBadgeHTML(filterName, texto, textoCurto);
      $container.append(badgeHTML);

      totalFilters++;
    });

    // Atualizar contador no ícone do modal
    const $countBadge = $("#filter-count-badge");
    if (totalFilters > 0) {
      $countBadge.text(totalFilters).show();
    } else {
      $countBadge.hide();
    }

    return totalFilters > 0;
  }
  
  // --- EVENT LISTENERS ---

  $("#filterForm").on("change.filter-badges", "input, select", renderBadges);
  
  $("#btn-filtrar-modal").on("click.filter-badges", function (e) {
    e.preventDefault();
    if (typeof FilterModal !== "undefined" && FilterModal.submitFilters) {
      FilterModal.submitFilters();
    } else {
      $("#filterModal").modal("hide");
      renderBadges();
    }
  });

  $("#btn-clear-filters").on("click.filter-badges", function () {
    $("#filterForm")[0].reset();
    $("#filterForm").find("select.selectpicker").selectpicker("deselectAll").selectpicker("refresh");
    $("#filterForm").find("input[type='date'], input[type='text']").val("");
    $("#filterForm").find("input[type='checkbox']").prop("checked", false);

    if (typeof FilterModalIntegration !== "undefined" && FilterModalIntegration.clearSavedFilters) {
      FilterModalIntegration.clearSavedFilters();
    }
    if (typeof FilterModal !== "undefined" && FilterModal.clearOptionsCache) {
      FilterModal.clearOptionsCache();
    }
    renderBadges();
    if ($("#loading-overlay").length) {
      $("#loading-overlay").show();
    }
    window.location.href = base_url + "atribuir_grupo?reset_filters=1";
  });

  $("#active-filters").on("click.filter-badges", ".badge-remove", function (e) {
    e.preventDefault();
    const filterName = $(this).data("name");
    const $form = $("#filterForm");
    
    // Limpar campo no formulário
    let $fields = $form.find(`[name="${filterName}"], [name="${filterName}[]"]`);
    $fields.each(function () {
      const $el = $(this);
      if ($el.is("select")) {
        $el.val([]);
        if ($el.hasClass("selectpicker")) $el.selectpicker("refresh");
      } else if ($el.is(":checkbox")) {
        $el.prop("checked", false);
      } else {
        $el.val("");
      }
    });

    // Limpar estado salvo
    if (typeof FilterModalIntegration !== "undefined" && FilterModalIntegration.updateWindowVariables) {
        let clearedValue = ""; // Valor padrão
        // A lógica original pode ser simplificada aqui se soubermos os tipos
        FilterModalIntegration.updateWindowVariables({ [filterName]: clearedValue });
    }

    // Remover o badge da UI
    $(this).closest(".filter-badge").remove();

    // Atualizar contador
    const remaining = $("#active-filters .filter-badge").length;
    const $countBadge = $("#filter-count-badge");
    if (remaining > 0) {
      $countBadge.text(remaining).show();
    } else {
      $countBadge.hide();
    }
  });

  // Inicialização
  setTimeout(function () {
    if (!renderBadgesFromSavedFilters()) {
      $("#active-filters").empty();
      $("#filter-count-badge").hide();
    }
  }, 200);
});
